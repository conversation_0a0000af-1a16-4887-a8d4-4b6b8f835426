# 皮口袋提现记录监听器 - 项目规划

## 项目概述

这是一个用于监听皮口袋平台提现充值记录的自动化工具，能够实时检测新增记录并进行相应处理。

## 项目目标

- **实时监听**：定期查询API接口，检测新增的提现充值记录
- **自动通知**：发现新记录时通过微信进行通知
- **稳定运行**：支持token自动刷新，确保长期稳定运行
- **易于使用**：提供简单的命令行界面

## 技术架构

### 核心组件

1. **WithdrawDepositMonitor** - 主监听器类
   - 负责API调用和数据处理
   - 管理token生命周期
   - 实现基准数据对比逻辑

2. **认证模块**
   - 自动登录获取token
   - token过期自动刷新
   - 请求头管理

3. **通知模块**
   - 微信消息推送
   - 错误处理和重试

### 数据流程

```
用户启动 → 登录获取token → 设置基准数据 → 定期查询 → 对比检测 → 发现新记录 → 微信通知
```

## 设计原则

- **可靠性优先**：完善的错误处理和异常恢复
- **模块化设计**：职责清晰，便于维护和扩展
- **用户友好**：清晰的日志输出和状态提示
- **配置灵活**：支持自定义监听间隔等参数

## 约束条件

- 依赖外部API的稳定性
- 需要有效的登录凭据
- 微信通知服务的可用性
- 网络连接的稳定性

## 扩展方向

- 支持多种通知方式（邮件、短信等）
- 添加数据持久化存储
- 实现Web管理界面
- 支持多账号监听
