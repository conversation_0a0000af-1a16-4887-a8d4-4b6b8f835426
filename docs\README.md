# 皮口袋提现记录监听器

一个用于监听皮口袋平台提现充值记录的自动化工具，支持实时检测新增记录并通过微信进行通知。

## 主要功能

- ✅ **实时监听**：定期查询API接口，检测新增的提现充值记录
- ✅ **自动登录**：支持用户名密码登录，token过期时自动刷新
- ✅ **微信通知**：发现新记录时自动发送微信消息通知
- ✅ **稳定运行**：完善的错误处理和异常恢复机制

## 快速开始

### 1. 运行程序

```bash
python main.py
```

### 2. 输入登录信息

程序启动后会提示输入：
- 登录账号
- 登录密码

### 3. 开始监听

程序会自动：
1. 使用提供的凭据登录获取token
2. 设置当前记录作为基准数据
3. 每30秒检查一次新增记录
4. 发现新记录时发送微信通知

## 工作原理

### 认证机制
- 使用用户名和密码进行登录
- 自动获取和管理API访问token
- token过期时自动重新登录

### 监听逻辑
1. **基准设置**：首次运行时获取当前所有记录作为基准
2. **定期检查**：每隔指定时间间隔查询最新记录
3. **差异对比**：将新查询结果与基准数据对比
4. **新记录处理**：发现新增记录时触发通知和处理逻辑

### 通知机制
- 集成微信消息推送API
- 新记录详情实时通知
- 支持错误状态通知

## 配置说明

### 监听间隔
默认30秒检查一次，可在 `start_monitoring()` 方法中修改：

```python
monitor.start_monitoring(interval=60)  # 改为60秒检查一次
```

### 微信通知配置
在 `wechat_notify()` 方法中配置：
- API密钥
- 接收用户ID
- 消息格式

## 测试

### 测试登录功能
```bash
python test/test_login.py
```

该测试脚本会：
1. 验证登录功能是否正常
2. 测试token获取
3. 验证API查询功能

## 错误处理

程序包含完善的错误处理机制：

- **网络错误**：自动重试和错误提示
- **认证失败**：清晰的登录错误信息
- **API异常**：详细的响应错误分析
- **JSON解析错误**：格式错误的友好提示

## 注意事项

1. **网络连接**：确保网络连接稳定
2. **登录凭据**：使用有效的账号和密码
3. **API可用性**：依赖目标API服务的稳定性
4. **微信服务**：需要微信通知服务正常运行

## 停止监听

按 `Ctrl+C` 可以安全停止监听程序。

## 项目结构

```
├── main.py              # 主程序文件
├── test/                # 测试文件目录
│   └── test_login.py    # 登录功能测试
└── docs/                # 文档目录
    ├── README.md        # 使用说明
    ├── PLANNING.md      # 项目规划
    └── TASK.md          # 任务记录
```
