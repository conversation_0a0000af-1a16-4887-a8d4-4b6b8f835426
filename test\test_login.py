"""
测试登录功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import WithdrawDepositMonitor


def test_login():
    """测试登录功能"""
    print("=== 测试登录功能 ===")
    
    # 配置参数
    BASE_URL = 'http://************:44371'
    
    # 获取用户输入
    print("请输入测试账号:")
    account = input().strip()
    
    print("请输入测试密码:")
    password = input().strip()
    
    if not account or not password:
        print("❌ 账号或密码不能为空")
        return
    
    try:
        # 创建监听器实例
        monitor = WithdrawDepositMonitor(BASE_URL, account, password)
        
        # 测试登录
        token = monitor.login()
        print(f"✅ 登录成功！")
        print(f"Token: {token[:50]}..." if len(token) > 50 else f"Token: {token}")
        
        # 测试查询记录（验证token是否有效）
        print("\n🔍 测试查询记录...")
        data = monitor.query_records(max_result_count=1)
        
        if 'result' in data:
            total_count = data.get('result', {}).get('totalCount', 0)
            print(f"✅ 查询成功！当前记录总数: {total_count}")
        else:
            print("⚠️ 查询结果格式异常")
            print(f"响应数据: {data}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_login()
