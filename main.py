"""
提现充值服务监听器
用于监听新增的提现充值记录并进行处理
"""

import requests
import json
import time
from datetime import datetime
from urllib.parse import quote
from typing import Dict, Optional, Tuple, List, Set


class WithdrawDepositMonitor:
    """提现充值服务监听器"""

    def __init__(self, base_url: str, account: str, password: str):
        """
        初始化监听器

        Args:
            base_url (str): API基础URL
            account (str): 登录账号
            password (str): 登录密码
        """
        self.base_url = base_url
        self.token = None
        self.account = account
        self.password = password
        self.session = requests.Session()
        self.baseline_records: Set[str] = set()  # 存储基准记录的ID
        self.is_baseline_set = False  # 标记是否已设置基准
        self._setup_headers()

    def _setup_headers(self) -> None:
        """设置请求头"""
        headers = {
            'Host': '************:44371',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'http://************:8001',
            'Referer': 'http://************:8001/',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        }

        # 如果有token，添加authorization头
        if self.token:
            headers['authorization'] = f'Bearer {self.token}'

        self.session.headers.update(headers)

    def get_today_time_range(self) -> Tuple[str, str]:
        """
        获取当天的时间范围（00:00:00 到 23:59:59）

        Returns:
            Tuple[str, str]: (开始时间, 结束时间)
        """
        today = datetime.now()
        start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)

        return (
            start_time.strftime('%Y/%m/%d %H:%M:%S'),
            end_time.strftime('%Y/%m/%d %H:%M:%S')
        )

    def build_query_url(self, skip_count: int = 0, max_result_count: int = 500,
                       start_time: Optional[str] = None, end_time: Optional[str] = None) -> str:
        """
        构建查询URL

        Args:
            skip_count (int): 跳过记录数
            max_result_count (int): 最大返回记录数
            start_time (Optional[str]): 开始时间，格式：YYYY/MM/DD HH:MM:SS
            end_time (Optional[str]): 结束时间，格式：YYYY/MM/DD HH:MM:SS

        Returns:
            str: 完整的查询URL
        """
        # 如果没有指定时间，使用当天时间范围
        if start_time is None or end_time is None:
            start_time, end_time = self.get_today_time_range()

        # URL编码时间参数
        start_time_encoded = quote(start_time)
        # end_time_encoded = quote(end_time)
        today = datetime.now()
        end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
        
        end_time_encoded = quote(end_time.strftime('%Y/%m/%d %H:%M:%S'))

        return (f'{self.base_url}/api/vnd/withdraw-deposit-app-servicecs'
                f'?skipCount={skip_count}'
                f'&maxResultCount={max_result_count}'
                f'&startTime={start_time_encoded}'
                f'&endTime={end_time_encoded}')


    def login(self) -> str:
        """
        登录获取token

        Returns:
            str: 获取到的token

        Raises:
            requests.RequestException: 登录请求失败
            KeyError: 响应格式不正确
        """
        headers = {
            'Host': '************:44371',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json; application/json;charset=UTF-8',
            'Origin': 'http://************:8001',
            'Referer': 'http://************:8001/',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        }

        json_data = {
            'userName': self.account,
            'password': self.password,
            'username': self.account,
        }

        try:
            print("🔐 正在登录获取新token...")
            response = requests.post('http://************:44371/api/vnd/user/user-login',
                                   headers=headers, json=json_data)
            response.raise_for_status()

            result = response.json()
            self.token = result['result']['tokenValue']

            # 更新session的请求头
            self._setup_headers()

            print("✅ 登录成功，token已更新")
            return self.token

        except requests.RequestException as e:
            print(f"❌ 登录请求失败: {e}")
            raise
        except (KeyError, TypeError) as e:
            print(f"❌ 登录响应格式错误: {e}")
            print(f"响应内容: {response.text if 'response' in locals() else 'N/A'}")
            raise


    def query_records(self, skip_count: int = 0, max_result_count: int = 500,
                     start_time: Optional[str] = None, end_time: Optional[str] = None) -> Dict:
        """
        查询提现充值记录

        Args:
            skip_count (int): 跳过记录数
            max_result_count (int): 最大返回记录数
            start_time (Optional[str]): 开始时间
            end_time (Optional[str]): 结束时间

        Returns:
            Dict: API响应数据

        Raises:
            requests.RequestException: 请求失败时抛出异常
        """
        url = self.build_query_url(skip_count, max_result_count, start_time, end_time)

        try:
            response = self.session.get(url)

            # 检查是否token过期
            if response.status_code == 401:
                print("⚠️ Token已过期，正在重新登录...")
                self.login()
                # 重新发起请求
                response = self.session.get(url)

            response.raise_for_status()  # 检查HTTP状态码
            return response.json()

        except requests.RequestException as e:
            print(f"❌ 请求失败: {e}")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"响应内容: {response.text}")
            raise

    def extract_record_ids(self, data: Dict) -> Set[str]:
        """
        从API响应中提取记录ID

        Args:
            data (Dict): API响应数据

        Returns:
            Set[str]: 记录ID集合
        """
        record_ids = set()
        if 'result' in data and 'items' in data['result']:
            for item in data['result']['items']:
                # 假设每条记录有唯一的ID字段，根据实际API调整
                if 'id' in item:
                    record_ids.add(str(item['id']))
                else:
                    # 如果没有ID字段，可以用其他唯一标识符组合
                    unique_key = f"{item.get('amount', '')}_{item.get('creationTime', '')}_{item.get('userId', '')}"
                    record_ids.add(unique_key)
        return record_ids

    def set_baseline(self) -> Dict:
        """
        设置基准数据（第一次查询的结果）

        Returns:
            Dict: 基准查询结果
        """
        print("🔍 正在获取基准数据...")
        data = self.query_records()
        self.baseline_records = self.extract_record_ids(data)
        self.is_baseline_set = True

        total_count = data.get('result', {}).get('totalCount', 0)
        print(f"✅ 基准数据已设置，当前记录数: {total_count}")

        return data

    def check_new_records(self) -> List[Dict]:
        """
        检查是否有新增记录

        Returns:
            List[Dict]: 新增的记录列表
        """
        if not self.is_baseline_set:
            raise ValueError("请先调用 set_baseline() 设置基准数据")

        current_data = self.query_records()
        current_records = self.extract_record_ids(current_data)

        # 找出新增的记录ID
        new_record_ids = current_records - self.baseline_records

        if not new_record_ids:
            return []

        # 从当前数据中提取新增的完整记录
        new_records = []
        if 'result' in current_data and 'items' in current_data['result']:
            for item in current_data['result']['items']:
                # 检查这条记录是否是新增的
                if 'id' in item:
                    record_id = str(item['id'])
                else:
                    record_id = f"{item.get('amount', '')}_{item.get('creationTime', '')}_{item.get('userId', '')}"
                    

                if record_id in new_record_ids:
                    new_records.append(item)
                    self.wechat_notify(f"发现新增记录: {json.dumps(item, ensure_ascii=False)}")

        # 更新基准数据
        self.baseline_records.update(new_record_ids)

        return new_records

    def process_new_records(self, new_records: List[Dict]) -> None:
        """
        处理新增记录的占位方法

        Args:
            new_records (List[Dict]): 新增的记录列表

        Note:
            这个方法需要用户根据具体需求实现
        """
        print(f"🆕 发现 {len(new_records)} 条新增记录:")
        for i, record in enumerate(new_records, 1):
            print(f"  {i}. {json.dumps(record, ensure_ascii=False)}")

        # TODO: 在这里添加您的具体处理逻辑
        print("⚠️  请在 process_new_records 方法中实现具体的处理逻辑")

    def start_monitoring(self, interval: int = 30) -> None:
        """
        开始监听新增记录

        Args:
            interval (int): 监听间隔（秒），默认30秒
        """
        print(f"🚀 开始监听，检查间隔: {interval}秒")
        print("按 Ctrl+C 停止监听\n")

        # 首次登录获取token
        if not self.token:
            self.login()

        # 设置基准数据
        self.set_baseline()

        try:
            while True:
                time.sleep(interval)

                print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 检查新增记录...")

                try:
                    new_records = self.check_new_records()

                    if new_records:
                        self.process_new_records(new_records)
                    else:
                        print("   没有新增记录")

                except Exception as e:
                    print(f"❌ 检查记录时出错: {e}")

        except KeyboardInterrupt:
            print("\n🛑 监听已停止")

    def wechat_notify(self, message: str) -> None:
        """
        微信通知

        Args:
            message (str): 通知内容
        """
        headers = {
            'Content-Type': 'application/json',
        }

        params = {
            'key': '57829984-0f25-4c6f-86e3-24c604db8eaa',
        }

        json_data = {
            'MsgItem': [
                {
                    'AtWxIDList': [
                        'string',
                    ],
                    'ImageContent': '',
                    'MsgType': 0,
                    'TextContent': f'{message}',
                    'ToUserName': 'wxid_0jjff1o139cc22',
                },
            ],
        }

        try:
            response = requests.post('http://8.133.242.147:6100/message/SendTextMessage',
                                   params=params, headers=headers, json=json_data)
            response.raise_for_status()
            print("✅ 微信通知发送成功")
        except requests.RequestException as e:
            print(f"❌ 微信通知发送失败: {e}")



def get_user_credentials() -> Tuple[str, str]:
    """
    获取用户输入的登录凭据

    Returns:
        Tuple[str, str]: (账号, 密码)
    """
    print("=== 提现充值监听器 ===")
    print("请输入登录账号:")
    account = input().strip()

    if not account:
        raise ValueError("账号不能为空")

    print("请输入登录密码:")
    password = input().strip()

    if not password:
        raise ValueError("密码不能为空")

    return account, password


def main():
    """主函数"""
    try:
        # 获取用户输入的登录凭据
        account, password = get_user_credentials()

        # 配置参数
        BASE_URL = 'http://************:44371'

        # 创建监听器实例
        monitor = WithdrawDepositMonitor(BASE_URL, account, password)

        # 开始监听
        monitor.start_monitoring(interval=30)  # 30秒检查一次

    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()